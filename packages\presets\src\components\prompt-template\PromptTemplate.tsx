import { Col, Row } from "antd";
import React, { useMemo } from "react";

import { BuildInCommand, useActiveAgentConfig, useActiveConversationId, useCommandRunner } from "@cscs-agent/core";

const TemplateModal: React.FC = () => {
  const activeAgentConfig = useActiveAgentConfig();
  const [activeConversationId] = useActiveConversationId();
  const runner = useCommandRunner();

  const cards = useMemo(() => {
    if (!activeAgentConfig) return [];
    return activeAgentConfig?.prompts ?? [];
  }, [activeAgentConfig]);

  const sendMessage = (message: string) => {
    runner(BuildInCommand.SendMessage, {
      message,
      conversationId: activeConversationId,
      agentCode: activeAgentConfig?.code,
      isNewConversation: !activeConversationId,
    });
    runner(BuildInCommand.CloseSenderHeaderPanel);
  };

  return (
    <div className="presets:w-full presets:bg-white">
      <Row gutter={[16, 16]}>
        {cards.map((card, index) => (
          <Col span={6} key={index}>
            <div
              className="presets:border presets:border-gray-200 presets:rounded-sm presets:h-[84px] presets:p-3 presets:cursor-pointer presets:hover:border-blue-500"
              onClick={() => sendMessage(card.prompt)}
            >
              <h4 className="presets:flex presets:font-medium presets:text-gray-800 presets:mb-1 presets:text-sm">
                <span className="presets:px-1 presets:text-sm presets:text-[#6C90F2] presets:rounded-sm presets:bg-[rgba(108,144,242,0.10)]">
                  {card.icon}
                </span>
                <span className="presets:ml-2">{card.title}</span>
              </h4>
              <p className="presets:text-xs presets:text-light presets:m-0 presets:leading-relaxed">
                {card.description}
              </p>
            </div>
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default TemplateModal;
