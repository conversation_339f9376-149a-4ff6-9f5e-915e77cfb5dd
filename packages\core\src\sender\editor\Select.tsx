import { Select as AntdSelect, Tooltip } from "antd";
import React, { useState } from "react";

import { Node, NodeViewProps, mergeAttributes } from "@tiptap/core";
import { NodeViewWrapper } from "@tiptap/react";
import { ReactNodeViewRenderer } from "@tiptap/react";

interface SelectProps {
  placeholder?: string;
  options: Array<{ label: string; value: string }>;
  defaultValue?: string;
  tooltips?: string;
  disabled?: boolean;
}

/**
 * Select component for TipTap editor - renders as an interactive dropdown
 */
const SelectComponent: React.FC<NodeViewProps> = (props) => {
  const { node, updateAttributes } = props;
  const { placeholder, options, defaultValue, tooltips, disabled } = node.attrs as SelectProps;

  const [selectedValue, setSelectedValue] = useState<string | undefined>(defaultValue);

  const handleChange = (value: string) => {
    setSelectedValue(value);
    // Update the node attributes to persist the selected value
    updateAttributes({ defaultValue: value });
  };

  const selectElement = (
    <AntdSelect
      placeholder={placeholder || "Please select..."}
      value={selectedValue}
      onChange={handleChange}
      disabled={disabled}
      style={{ minWidth: 120 }}
      size="small"
      options={options}
      className="ag:text-sm"
    />
  );

  return (
    <NodeViewWrapper as="span" className="ag:inline-block ag:mx-1">
      {tooltips ? <Tooltip title={tooltips}>{selectElement}</Tooltip> : selectElement}
    </NodeViewWrapper>
  );
};

const Select = Node.create({
  name: "Select",
  group: "inline",
  inline: true,
  atom: true, // Prevents the content from being editable

  addAttributes() {
    return {
      placeholder: {
        default: "Please select...",
      },
      options: {
        parseHTML: (element) => JSON.parse(element.getAttribute("options") || "[]"),
        default: [],
      },
      defaultValue: {
        default: undefined,
      },
      tooltips: {
        default: "",
      },
      disabled: {
        default: false,
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: "embedded-select",
      },
    ];
  },

  renderText(props) {
    const { defaultValue, placeholder } = props.node.attrs;
    return defaultValue || placeholder || "Select";
  },

  renderHTML({ HTMLAttributes }) {
    return ["embedded-select", mergeAttributes(HTMLAttributes)];
  },

  addNodeView() {
    return ReactNodeViewRenderer(SelectComponent, {});
  },
});

export default Select;