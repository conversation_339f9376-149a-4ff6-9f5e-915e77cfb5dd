import React, { forwardRef, useImperativeHandle } from "react";

import { EditorContent, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";

import EditableTag from "./EditableTag";
import Select from "./Select";
import Tag from "./Tag";

interface EditorProps {
  onChange: (value: string) => void;
  ref?: React.Ref<EditorRef>;
}

export interface EditorRef {
  focus: () => void;
  clear: () => void;
  insertText: (text: string) => void;
  getText: () => string;
}

const Editor = forwardRef<EditorRef, EditorProps>(function Editor(props, ref) {
  const { onChange } = props;

  const editor = useEditor({
    extensions: [StarterKit, Tag, EditableTag, Select],
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());
    },
    editorProps: {
      attributes: {
        style: "width: 100%; min-height: 114px; font-size: 14px;",
      },
    },
  });

  useImperativeHandle(
    ref,
    () => ({
      focus: () => {
        editor?.commands.focus();
      },
      clear: () => {
        editor?.commands.clearContent();
      },
      insertText: (text: string) => {
        editor?.commands.insertContent(text);
      },
      getText: () => {
        return editor?.getText() ?? "";
      },
    }),
    [],
  );

  return <EditorContent editor={editor} />;
});

export default Editor;
